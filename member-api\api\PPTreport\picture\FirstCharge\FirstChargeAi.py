# -*- coding: utf-8 -*-
"""
首次充值和消费数据AI分析器
提供首次充值和消费数据的智能分析功能
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional

from services.llm_service import LLMService
from .FirstChargePromt import FirstChargePrompt

logger = logging.getLogger(__name__)

class FirstChargeAiAnalyzer:
    """首次充值和消费数据AI分析器"""

    def __init__(self):
        """初始化首次充值和消费数据AI分析器"""
        self.llm_service = LLMService()
        self.prompt_provider = FirstChargePrompt()
        logger.info("首次充值和消费数据AI分析器初始化完成")

    async def analyze_first_member_number_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年首次充值和消费人数数据
        
        Args:
            monthly_data: 去年月度数据列表，包含首次充值、再次充值、首次消费、再次消费人数
        
        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年首次充值和消费数据分析：数据不足，无法进行有效分析。"

            # 获取提示词
            base_prompt = self.prompt_provider.get_first_member_number_last_year_prompt()
            
            # 构建完整的AI分析请求
            full_prompt = f"""
{base_prompt}

数据概况：
{self._format_first_member_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在30字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：首充转化率、复购率趋势、季节性特征、优化空间
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点30字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)
            
            if analysis_result and analysis_result.strip():
                logger.info("去年首次充值和消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "去年首次充值和消费数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"去年首次充值和消费数据AI分析失败: {str(e)}")
            return f"去年首次充值和消费数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def analyze_first_member_number_this_year_data(
        self, 
        this_year_data: List[Dict[str, Any]], 
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年首次充值和消费人数数据（包含对比分析）
        
        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年同期数据列表（用于对比）
        
        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年首次充值和消费数据分析：数据不足，无法进行有效分析。"

            # 获取提示词
            base_prompt = self.prompt_provider.get_first_member_number_this_year_prompt()
            
            # 构建对比数据部分
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_first_member_data_summary(last_year_data[:len(this_year_data)])}
"""

            # 构建完整的AI分析请求
            full_prompt = f"""
{base_prompt}

今年数据概况：
{self._format_first_member_data_summary(this_year_data)}

{comparison_section}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在30字以内（标点符号不计入字数），突出动态变化和改进建议
3. 重点关注：同比增长率、转化率变化、复购率提升、近期趋势
4. 必须标注具体数值和变化率
5. 每个洞察格式：动态变化 + 具体数据 + 优化建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点30字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)
            
            if analysis_result and analysis_result.strip():
                logger.info("今年首次充值和消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "今年首次充值和消费数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"今年首次充值和消费数据AI分析失败: {str(e)}")
            return f"今年首次充值和消费数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def generate_all_first_member_number_analysis(
        self, 
        this_year_data: List[Dict[str, Any]], 
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成全部首次充值和消费人数分析（包括去年和今年）
        
        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年月度数据列表
        
        Returns:
            Dict: 包含两个分析报告的字典
        """
        try:
            logger.info("开始生成首次充值和消费人数全部AI分析")
            
            # 并发执行两个分析任务
            tasks = [
                self.analyze_first_member_number_last_year_data(last_year_data),
                self.analyze_first_member_number_this_year_data(this_year_data, last_year_data)
            ]
            
            results = await asyncio.gather(*tasks)
            
            analysis_dict = {
                "first_member_number_last_year_analysis_report": results[0],
                "first_member_number_this_year_analysis_report": results[1]
            }
            
            logger.info("首次充值和消费人数全部AI分析完成")
            return analysis_dict
            
        except Exception as e:
            logger.error(f"生成首次充值和消费人数全部AI分析失败: {str(e)}")
            return {
                "first_member_number_last_year_analysis_report": "AI分析服务暂时不可用",
                "first_member_number_this_year_analysis_report": "AI分析服务暂时不可用"
            }

    def _format_first_member_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化首次充值和消费数据摘要
        
        Args:
            monthly_data: 月度数据列表
        
        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"
        
        summary = []
        total_first_charge = 0
        total_repeat_charge = 0
        total_first_consume = 0
        total_repeat_consume = 0
        
        for item in monthly_data:
            month = item.get('month', '未知月份')
            first_charge = item.get('first_charge_count', 0)
            repeat_charge = item.get('repeat_charge_count', 0)
            first_consume = item.get('first_consume_count', 0)
            repeat_consume = item.get('repeat_consume_count', 0)
            
            # 计算转化率
            charge_to_consume_rate = 0
            if first_charge > 0:
                charge_to_consume_rate = (first_consume / first_charge) * 100
            
            # 计算复购率
            repeat_rate = 0
            total_charge = first_charge + repeat_charge
            if total_charge > 0:
                repeat_rate = (repeat_charge / total_charge) * 100
            
            summary.append(f"{month}: 首充{first_charge}人, 再充{repeat_charge}人, "
                         f"首消{first_consume}人, 再消{repeat_consume}人, "
                         f"首充转化率{charge_to_consume_rate:.1f}%, 复购占比{repeat_rate:.1f}%")
            
            total_first_charge += first_charge
            total_repeat_charge += repeat_charge
            total_first_consume += first_consume
            total_repeat_consume += repeat_consume
        
        # 添加汇总信息
        total_charge = total_first_charge + total_repeat_charge
        total_consume = total_first_consume + total_repeat_consume
        
        avg_charge_to_consume = 0
        if total_first_charge > 0:
            avg_charge_to_consume = (total_first_consume / total_first_charge) * 100
        
        avg_repeat_rate = 0
        if total_charge > 0:
            avg_repeat_rate = (total_repeat_charge / total_charge) * 100
        
        summary.append(f"\n汇总: 总首充{total_first_charge}人, 总再充{total_repeat_charge}人, "
                      f"总首消{total_first_consume}人, 总再消{total_repeat_consume}人")
        summary.append(f"平均首充转化率: {avg_charge_to_consume:.1f}%, 平均复购占比: {avg_repeat_rate:.1f}%")
        
        return "\n".join(summary)

    def _calculate_growth_rate(self, current: float, previous: float) -> float:
        """
        计算增长率
        
        Args:
            current: 当前值
            previous: 之前值
        
        Returns:
            float: 增长率（百分比）
        """
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return ((current - previous) / previous) * 100

    async def analyze_first_charge_sale_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年首次充值和消费金额数据
        
        Args:
            monthly_data: 去年月度数据列表，包含首次充值、再次充值、首次消费、再次消费金额
        
        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年首次充值和消费金额分析：数据不足，无法进行有效分析。"

            # 获取提示词
            base_prompt = self.prompt_provider.get_first_charge_sale_last_year_prompt()
            
            # 构建完整的AI分析请求
            full_prompt = f"""
{base_prompt}

数据概况：
{self._format_first_charge_sale_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在30字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：首充金额趋势、消费金额转化、金额结构分析、盈利能力评估
4. 必须标注具体金额和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点30字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)
            
            if analysis_result and analysis_result.strip():
                logger.info("去年首次充值和消费金额AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "去年首次充值和消费金额分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"去年首次充值和消费金额AI分析失败: {str(e)}")
            return f"去年首次充值和消费金额分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def analyze_first_charge_sale_this_year_data(
        self, 
        this_year_data: List[Dict[str, Any]], 
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年首次充值和消费金额数据（包含对比分析）
        
        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年同期数据列表（用于对比）
        
        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年首次充值和消费金额分析：数据不足，无法进行有效分析。"

            # 获取提示词
            base_prompt = self.prompt_provider.get_first_charge_sale_this_year_prompt()
            
            # 构建对比数据部分
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_first_charge_sale_data_summary(last_year_data[:len(this_year_data)])}
"""

            # 构建完整的AI分析请求
            full_prompt = f"""
{base_prompt}

今年数据概况：
{self._format_first_charge_sale_data_summary(this_year_data)}

{comparison_section}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在30字以内（标点符号不计入字数），突出动态变化和改进建议
3. 重点关注：同比金额增长、充值消费转化效率、客单价变化、收益质量提升
4. 必须标注具体金额和变化率
5. 每个洞察格式：动态变化 + 具体数据 + 优化建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点30字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)
            
            if analysis_result and analysis_result.strip():
                logger.info("今年首次充值和消费金额AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "今年首次充值和消费金额分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"今年首次充值和消费金额AI分析失败: {str(e)}")
            return f"今年首次充值和消费金额分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def generate_all_first_charge_sale_analysis(
        self, 
        this_year_data: List[Dict[str, Any]], 
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成全部首次充值和消费金额分析（包括去年和今年）
        
        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年月度数据列表
        
        Returns:
            Dict: 包含两个分析报告的字典
        """
        try:
            logger.info("开始生成首次充值和消费金额全部AI分析")
            
            # 并发执行两个分析任务
            tasks = [
                self.analyze_first_charge_sale_last_year_data(last_year_data),
                self.analyze_first_charge_sale_this_year_data(this_year_data, last_year_data)
            ]
            
            results = await asyncio.gather(*tasks)
            
            analysis_dict = {
                "first_charge_last_year_analysis_report": results[0],
                "first_charge_this_year_analysis_report": results[1]
            }
            
            logger.info("首次充值和消费金额全部AI分析完成")
            return analysis_dict
            
        except Exception as e:
            logger.error(f"生成首次充值和消费金额全部AI分析失败: {str(e)}")
            return {
                "first_charge_last_year_analysis_report": "AI分析服务暂时不可用",
                "first_charge_this_year_analysis_report": "AI分析服务暂时不可用"
            }

    def _format_first_charge_sale_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化首次充值和消费金额数据摘要
        
        Args:
            monthly_data: 月度数据列表
        
        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"
        
        summary = []
        total_first_charge_amount = 0.0
        total_repeat_charge_amount = 0.0
        total_first_consume_amount = 0.0
        total_repeat_consume_amount = 0.0
        
        for item in monthly_data:
            month = item.get('month', '未知月份')
            first_charge_amount = item.get('first_charge_amount', 0.0)
            repeat_charge_amount = item.get('repeat_charge_amount', 0.0)
            first_consume_amount = item.get('first_consume_amount', 0.0)
            repeat_consume_amount = item.get('repeat_consume_amount', 0.0)
            
            # 计算充值消费转化率
            charge_to_consume_rate = 0.0
            total_charge_month = first_charge_amount + repeat_charge_amount
            total_consume_month = first_consume_amount + repeat_consume_amount
            if total_charge_month > 0:
                charge_to_consume_rate = (total_consume_month / total_charge_month) * 100
            
            # 计算再次充值占比
            repeat_charge_rate = 0.0
            if total_charge_month > 0:
                repeat_charge_rate = (repeat_charge_amount / total_charge_month) * 100
            
            summary.append(f"{month}: 首充{first_charge_amount:.0f}元, 再充{repeat_charge_amount:.0f}元, "
                         f"首消{first_consume_amount:.0f}元, 再消{repeat_consume_amount:.0f}元, "
                         f"充值消费转化率{charge_to_consume_rate:.1f}%, 再充占比{repeat_charge_rate:.1f}%")
            
            total_first_charge_amount += first_charge_amount
            total_repeat_charge_amount += repeat_charge_amount
            total_first_consume_amount += first_consume_amount
            total_repeat_consume_amount += repeat_consume_amount
        
        # 添加汇总信息
        total_charge_amount = total_first_charge_amount + total_repeat_charge_amount
        total_consume_amount = total_first_consume_amount + total_repeat_consume_amount
        
        avg_charge_to_consume = 0.0
        if total_charge_amount > 0:
            avg_charge_to_consume = (total_consume_amount / total_charge_amount) * 100
        
        avg_repeat_rate = 0.0
        if total_charge_amount > 0:
            avg_repeat_rate = (total_repeat_charge_amount / total_charge_amount) * 100
        
        summary.append(f"\n汇总: 总首充{total_first_charge_amount:.0f}元, 总再充{total_repeat_charge_amount:.0f}元, "
                      f"总首消{total_first_consume_amount:.0f}元, 总再消{total_repeat_consume_amount:.0f}元")
        summary.append(f"总充值消费转化率: {avg_charge_to_consume:.1f}%, 总再充占比: {avg_repeat_rate:.1f}%")
        
        return "\n".join(summary)