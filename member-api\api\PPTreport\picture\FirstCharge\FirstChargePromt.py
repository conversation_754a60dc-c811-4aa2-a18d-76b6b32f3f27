# -*- coding: utf-8 -*-
"""
首次充值和消费AI分析提示词配置
提供专业的按点分析模板，包含洞察性和实操性建议
"""

from typing import Dict, Any, List
import random

class FirstChargePrompt:
    """首次充值和消费分析提示词"""
    
    # 首次充值和消费数据分析模板句式（去年数据）
    FIRST_CHARGE_LAST_YEAR_TEMPLATES = [
        "去年全年首充表现，月均首次充值{avg_first_charge:.0f}人，全年累计{total_first_charge}人，{trend_analysis}。",
        "去年充值结构显示，首充占比{first_charge_ratio:.1f}%，复充占比{repeat_charge_ratio:.1f}%，{structure_analysis}。",
        "去年消费转化表现为首充到首消转化率{conversion_rate:.1f}%，{conversion_quality}，{conversion_trend}。",
        "去年复购行为呈现{repeat_pattern}特征，{seasonal_analysis}，{behavior_stability}。",
        "去年数据基础表明{baseline_analysis}，为今年运营{baseline_significance}。"
    ]
    
    # 首次充值和消费数据分析模板句式（今年数据，包含对比）
    FIRST_CHARGE_THIS_YEAR_TEMPLATES = [
        "今年截至目前首充人数累计{total_first_charge}人，月均{avg_first_charge:.0f}人，较去年同期{comparison_trend}，{performance_analysis}。",
        "今年充值结构优化，首充占比{first_charge_ratio:.1f}%，复充占比{repeat_charge_ratio:.1f}%，相比去年{structure_comparison}，{efficiency_improvement}。",
        "今年转化效率{conversion_trend}，首充到首消转化率{conversion_rate:.1f}%，{monthly_conversion_performance}，{conversion_quality_analysis}。",
        "对比去年同期，今年{year_over_year_analysis}，{competitive_position}。",
        "今年充值消费发展{development_assessment}，{future_projection}。"
    ]
    
    # 数据趋势分析词汇
    TREND_ANALYSIS_TERMS = {
        "positive": ["呈现稳步上升趋势", "表现出良好增长态势", "显示积极发展势头", "展现强劲增长动力"],
        "negative": ["出现下降趋势", "表现相对疲软", "增长动力不足", "面临增长挑战"],
        "stable": ["保持相对稳定", "波动较小", "增长平稳", "发展稳健"],
        "volatile": ["波动较大", "起伏明显", "变化频繁", "不够稳定"]
    }
    
    # 转化质量分析词汇
    CONVERSION_QUALITY_TERMS = [
        "转化效率优秀，用户质量较高",
        "转化表现良好，有提升空间",
        "转化率偏低，需要优化策略",
        "转化效果不理想，需重点改善"
    ]
    
    # 对比分析词汇
    COMPARISON_TERMS = {
        "better": ["表现更优", "有所改善", "明显提升", "显著改进"],
        "worse": ["有所下降", "表现不如", "需要改进", "存在差距"],
        "similar": ["基本持平", "相差不大", "保持稳定", "变化不明显"]
    }
    
    @staticmethod
    def get_first_member_number_last_year_prompt(monthly_data: List[Dict[str, Any]] = None) -> str:
        """
        生成去年首次充值和消费数据分析的按点格式提示词（洞见性+实操性）
        
        Args:
            monthly_data: 去年月度数据列表（可选）
        
        Returns:
            str: 按点格式的分析提示词
        """
        if monthly_data and len(monthly_data) > 0:
            # 计算统计数据
            first_charge_counts = [item.get('first_charge_count', 0) for item in monthly_data]
            repeat_charge_counts = [item.get('repeat_charge_count', 0) for item in monthly_data]
            first_consume_counts = [item.get('first_consume_count', 0) for item in monthly_data]
            repeat_consume_counts = [item.get('repeat_consume_count', 0) for item in monthly_data]
            
            avg_first_charge = sum(first_charge_counts) / len(first_charge_counts) if first_charge_counts else 0
            avg_repeat_charge = sum(repeat_charge_counts) / len(repeat_charge_counts) if repeat_charge_counts else 0
            avg_first_consume = sum(first_consume_counts) / len(first_consume_counts) if first_consume_counts else 0
            avg_repeat_consume = sum(repeat_consume_counts) / len(repeat_consume_counts) if repeat_consume_counts else 0
            
            total_first_charge = sum(first_charge_counts)
            total_repeat_charge = sum(repeat_charge_counts)
            total_first_consume = sum(first_consume_counts)
            
            # 计算关键指标
            conversion_rate = (total_first_consume / total_first_charge * 100) if total_first_charge > 0 else 0
            first_charge_ratio = (total_first_charge / (total_first_charge + total_repeat_charge) * 100) if (total_first_charge + total_repeat_charge) > 0 else 0
            
            # 找出峰值和低谷
            if first_charge_counts:
                max_idx = first_charge_counts.index(max(first_charge_counts))
                min_idx = first_charge_counts.index(min(first_charge_counts))
                peak_month = monthly_data[max_idx].get('month', '未知')
                valley_month = monthly_data[min_idx].get('month', '未知')
                peak_value = max(first_charge_counts)
                valley_value = min(first_charge_counts)
            else:
                peak_month = valley_month = "未知"
                peak_value = valley_value = 0
            
            # 计算波动性
            variance = sum((x - avg_first_charge) ** 2 for x in first_charge_counts) / len(first_charge_counts) if first_charge_counts else 0
            volatility = (variance ** 0.5) / avg_first_charge * 100 if avg_first_charge > 0 else 0
            
            # 生成按点分析
            analysis_points = []
            
            # 1. 充值规模洞察 + 实操建议
            if avg_first_charge > 500:
                analysis_points.append(f"• 首充规模优异：月均首充{avg_first_charge:.0f}人，全年累计{total_first_charge}人，建议维持获客策略并优化转化路径")
            elif avg_first_charge > 200:
                analysis_points.append(f"• 首充规模稳健：月均首充{avg_first_charge:.0f}人，有提升空间，建议加强推广渠道并优化注册流程")
            else:
                analysis_points.append(f"• 首充规模偏低：月均首充{avg_first_charge:.0f}人，需重点关注，建议重新评估获客策略并增加引流投入")
            
            # 2. 转化效率洞察 + 实操建议
            if conversion_rate > 80:
                analysis_points.append(f"• 转化效率优秀：首充到首消转化率{conversion_rate:.1f}%，用户质量高，建议推出阶梯优惠巩固用户")
            elif conversion_rate > 60:
                analysis_points.append(f"• 转化效率良好：首充到首消转化率{conversion_rate:.1f}%，有优化空间，建议优化首充后引导流程")
            else:
                analysis_points.append(f"• 转化效率偏低：首充到首消转化率{conversion_rate:.1f}%，需要改进，建议分析转化障碍并优化产品体验")
            
            # 3. 充值结构洞察 + 实操建议
            if first_charge_ratio > 40:
                analysis_points.append(f"• 新客占比高：首充占总充值{first_charge_ratio:.1f}%，获客能力强，建议加强新客留存策略")
            elif first_charge_ratio > 25:
                analysis_points.append(f"• 新客占比适中：首充占总充值{first_charge_ratio:.1f}%，结构合理，建议平衡新客获取和老客维护")
            else:
                analysis_points.append(f"• 新客占比偏低：首充占总充值{first_charge_ratio:.1f}%，需加强拉新，建议优化获客渠道和新手优惠")
            
            # 4. 季节性波动洞察 + 实操建议
            if volatility > 30:
                analysis_points.append(f"• 波动性较大：首充人数波动{volatility:.1f}%，峰值{peak_month}({peak_value}人)，建议制定淡季促销策略")
            else:
                analysis_points.append(f"• 波动性适中：首充人数相对稳定，波动{volatility:.1f}%，建议保持现有节奏并适度优化")
            
            return '\n'.join(analysis_points)
        
        else:
            # 返回原始格式的提示词
            return """
分析去年首次充值和消费人数数据，生成按点格式的专业分析：

要求：
1. 使用"•"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察包含：数据发现 + 核心指标 + 实操建议
3. 重点关注：首充规模、转化效率、充值结构、季节性特征
4. 必须标注具体数值和关键指标
5. 每个洞察控制在40字以内，突出实用性

数据维度：
- 首次充值人数及趋势
- 首充到首消转化率
- 首充与复充结构比例
- 月度波动性和季节性特征
"""
    
    @staticmethod
    def get_first_member_number_this_year_prompt(
        this_year_data: List[Dict[str, Any]] = None,
        last_year_data: List[Dict[str, Any]] = None
    ) -> str:
        """
        生成今年首次充值和消费数据分析的按点格式提示词（包含对比分析）
        
        Args:
            this_year_data: 今年月度数据列表（可选）
            last_year_data: 去年同期数据列表（可选）
        
        Returns:
            str: 按点格式的对比分析提示词
        """
        if this_year_data and len(this_year_data) > 0:
            # 计算今年统计数据
            this_first_charge = [item.get('first_charge_count', 0) for item in this_year_data]
            this_repeat_charge = [item.get('repeat_charge_count', 0) for item in this_year_data]
            this_first_consume = [item.get('first_consume_count', 0) for item in this_year_data]
            this_repeat_consume = [item.get('repeat_consume_count', 0) for item in this_year_data]
            
            this_avg_first_charge = sum(this_first_charge) / len(this_first_charge)
            this_total_first_charge = sum(this_first_charge)
            this_total_first_consume = sum(this_first_consume)
            this_conversion_rate = (this_total_first_consume / this_total_first_charge * 100) if this_total_first_charge > 0 else 0
            
            # 计算去年同期对比数据
            growth_first_charge = 0
            conversion_change = 0
            
            if last_year_data and len(last_year_data) > 0:
                # 取去年同期数据
                last_year_same_period = last_year_data[:len(this_year_data)]
                
                last_first_charge = [item.get('first_charge_count', 0) for item in last_year_same_period]
                last_first_consume = [item.get('first_consume_count', 0) for item in last_year_same_period]
                
                last_avg_first_charge = sum(last_first_charge) / len(last_first_charge) if last_first_charge else 0
                last_total_first_charge = sum(last_first_charge)
                last_total_first_consume = sum(last_first_consume)
                last_conversion_rate = (last_total_first_consume / last_total_first_charge * 100) if last_total_first_charge > 0 else 0
                
                # 计算增长率
                growth_first_charge = ((this_avg_first_charge - last_avg_first_charge) / last_avg_first_charge * 100) if last_avg_first_charge > 0 else 0
                conversion_change = this_conversion_rate - last_conversion_rate
            
            # 生成按点分析
            analysis_points = []
            
            # 1. 增长对比洞察 + 实操建议
            if growth_first_charge > 20:
                analysis_points.append(f"• 增长表现卓越：首充人数较去年同期增长{growth_first_charge:.1f}%，累计{this_total_first_charge}人，建议加大成功策略投入")
            elif growth_first_charge > 5:
                analysis_points.append(f"• 增长表现良好：首充人数较去年同期增长{growth_first_charge:.1f}%，建议分析增长驱动因素并复制经验")
            elif growth_first_charge > -5:
                analysis_points.append(f"• 增长表现平稳：首充人数较去年同期变化{growth_first_charge:.1f}%，建议优化获客策略提升增长")
            else:
                analysis_points.append(f"• 增长表现下滑：首充人数较去年同期下降{abs(growth_first_charge):.1f}%，建议紧急分析原因并制定改进策略")
            
            # 2. 转化效率对比洞察 + 实操建议
            if conversion_change > 5:
                analysis_points.append(f"• 转化效率提升：首充到首消转化率达{this_conversion_rate:.1f}%，较去年提升{conversion_change:.1f}个百分点，建议继续优化")
            elif conversion_change > -5:
                analysis_points.append(f"• 转化效率稳定：首充到首消转化率{this_conversion_rate:.1f}%，与去年基本持平，建议深化用户引导")
            else:
                analysis_points.append(f"• 转化效率下降：首充到首消转化率{this_conversion_rate:.1f}%，较去年下降{abs(conversion_change):.1f}个百分点，需重点优化")
            
            # 3. 最新趋势洞察 + 实操建议
            recent_months = this_first_charge[-3:] if len(this_first_charge) >= 3 else this_first_charge
            recent_avg = sum(recent_months) / len(recent_months) if recent_months else 0
            trend_change = ((recent_avg - this_avg_first_charge) / this_avg_first_charge * 100) if this_avg_first_charge > 0 else 0
            
            if trend_change > 10:
                analysis_points.append(f"• 近期势头强劲：最近月份首充人数环比增长{trend_change:.1f}%，建议把握增长机遇扩大规模")
            elif trend_change > -10:
                analysis_points.append(f"• 近期表现平稳：最近月份首充人数基本稳定，建议推出新活动刺激增长")
            else:
                analysis_points.append(f"• 近期出现下滑：最近月份首充人数环比下降{abs(trend_change):.1f}%，建议立即调整策略")
            
            # 4. 复购发展洞察 + 实操建议
            repeat_ratio = (sum(this_repeat_charge) / (sum(this_first_charge) + sum(this_repeat_charge)) * 100) if (sum(this_first_charge) + sum(this_repeat_charge)) > 0 else 0
            
            if repeat_ratio > 60:
                analysis_points.append(f"• 复购表现优秀：复充占比{repeat_ratio:.1f}%，用户忠诚度高，建议推出会员体系深化价值")
            elif repeat_ratio > 40:
                analysis_points.append(f"• 复购表现良好：复充占比{repeat_ratio:.1f}%，有提升空间，建议加强用户运营提升复购")
            else:
                analysis_points.append(f"• 复购有待提升：复充占比{repeat_ratio:.1f}%，需要关注，建议优化留存策略和复购激励")
            
            return '\n'.join(analysis_points)
        
        else:
            # 返回原始格式的提示词
            return """
分析今年首次充值和消费人数数据，结合去年同期对比，生成按点格式的专业分析：

要求：
1. 使用"•"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察包含：对比发现 + 核心数据 + 改进建议
3. 重点关注：同比增长率、转化效率变化、近期趋势、复购发展
4. 必须标注具体数值和变化百分比
5. 每个洞察控制在40字以内，突出动态变化

对比维度：
- 首充人数同比增长率
- 转化率同比变化
- 近期月份环比趋势
- 充值结构优化情况
"""
    
    @staticmethod
    def get_comparative_analysis_prompt():
        """获取对比分析提示词"""
        return """
对比分析今年与去年的首次充值和消费数据，生成专业洞察：

分析框架：
• 增长动能：评估首充人数、复充人数的同比变化
• 转化效率：分析首充到首消转化率的改善情况
• 结构优化：对比新老客户比例的变化趋势
• 运营效果：评估各项策略的实际成效

输出要求：
- 每个洞察用"•"开头
- 包含具体数据和变化率
- 提供可执行的优化建议
- 控制在40字以内
"""

    @staticmethod
    def get_first_charge_sale_last_year_prompt(monthly_data: List[Dict[str, Any]] = None) -> str:
        """
        生成去年首次充值和消费金额数据分析的按点格式提示词（洞见性+实操性）
        
        Args:
            monthly_data: 去年月度数据列表（可选）
        
        Returns:
            str: 按点格式的分析提示词
        """
        return """
分析去年首次充值和消费金额数据，生成按点格式的专业分析：

要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察包含：数据发现 + 核心指标 + 实操建议
3. 重点关注：充值金额规模、消费转化效率、金额结构、波动性特征
4. 必须标注具体金额和关键指标
5. 每个洞察控制在30字以内，突出实用性

数据维度：
- 首次充值金额及趋势
- 充值到消费转化效率
- 首充与复充金额比例
- 月度金额波动性和季节性特征
"""

    @staticmethod
    def get_first_charge_sale_this_year_prompt(
        this_year_data: List[Dict[str, Any]] = None,
        last_year_data: List[Dict[str, Any]] = None
    ) -> str:
        """
        生成今年首次充值和消费金额数据分析的按点格式提示词（包含对比分析）
        
        Args:
            this_year_data: 今年月度数据列表（可选）
            last_year_data: 去年同期数据列表（可选）
        
        Returns:
            str: 按点格式的对比分析提示词
        """
        return """
分析今年首次充值和消费金额数据，结合去年同期对比，生成按点格式的专业分析：

要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察包含：对比发现 + 核心数据 + 改进建议
3. 重点关注：同比金额增长、消费转化效率、近期趋势、充值结构
4. 必须标注具体金额和变化百分比
5. 每个洞察控制在30字以内，突出动态变化

对比维度：
- 首充金额同比增长率
- 消费转化率同比变化
- 近期月份金额环比趋势
- 充值结构优化情况
"""