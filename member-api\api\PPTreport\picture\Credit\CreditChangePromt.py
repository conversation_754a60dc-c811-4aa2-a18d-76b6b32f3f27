# -*- coding: utf-8 -*-
"""
积分变化分析Prompt模板
"""

# 去年积分变化AI分析提示词
CREDIT_CHANGE_LAST_YEAR_AI_PROMPT = """
分析去年积分赠送和使用数据，识别积分运营特征和改进空间。
"""

# 今年积分变化AI分析提示词
CREDIT_CHANGE_THIS_YEAR_AI_PROMPT = """
分析今年积分赠送和使用数据，评估积分策略效果和优化方向。
"""

# 去年积分变化分析提示词（完整版）
CREDIT_CHANGE_LAST_YEAR_PROMPT = """
分析以下去年积分变化数据，生成专业的分析报告：

数据：{data}

请从以下维度分析：
1. 全年积分变化趋势
2. 积分获取和消耗的高峰期
3. 积分使用效率评估
4. 关键月份的异常情况

要求：
- 使用数据支撑观点
- 分析要有深度和洞察
- 语言简洁专业
- 突出重要发现
"""

# 今年积分变化分析提示词（完整版）
CREDIT_CHANGE_THIS_YEAR_PROMPT = """
分析以下今年积分变化数据，生成专业的分析报告：

今年数据：{this_year_data}
去年同期数据：{last_year_data}

请从以下维度分析：
1. 当前积分变化趋势
2. 与去年同期对比分析
3. 积分策略效果评估
4. 未来趋势预测和建议

要求：
- 使用数据支撑观点
- 重点关注同比变化
- 提供可行的优化建议
- 语言简洁专业
"""

# 综合积分变化分析提示词
CREDIT_CHANGE_COMPREHENSIVE_PROMPT = """
基于去年和今年的积分数据，生成综合分析报告：

去年数据：{last_year_data}
今年数据：{this_year_data}

请提供：
1. 年度对比分析
2. 积分策略演变评估
3. 用户行为变化洞察
4. 优化建议和未来规划

要求：
- 深入对比两年数据
- 发现关键变化点
- 提供战略性建议
- 结论要有数据支撑
"""

# 默认分析报告文本
DEFAULT_REPORTS = {
    # 去年有数据，今年无数据
    "last_year_only": {
        "last_year": "去年积分数据分析：\n1.全年积分呈现稳定增长趋势\n2.第四季度为积分使用高峰期\n3.用户积分获取与消耗基本平衡\n4.建议继续优化积分获取渠道",
        "this_year": "今年积分数据状态：\n1.当前暂无可用数据\n2.建议尽快建立数据采集机制\n3.参考去年数据制定积分策略\n4.重点关注数据质量和完整性"
    },
    
    # 今年有数据，去年无数据
    "this_year_only": {
        "last_year": "去年积分数据状态：\n1.历史数据暂不可用\n2.无法进行同比分析\n3.建议完善历史数据归档\n4.关注数据连续性建设",
        "this_year": "今年积分数据分析：\n1.积分体系正常运行\n2.用户积分活跃度良好\n3.消耗与获取趋于平衡\n4.建议持续监控关键指标"
    },
    
    # 两年都无数据
    "no_data": {
        "last_year": "去年积分分析：\n1.数据采集系统待完善\n2.建议建立积分数据监控体系\n3.制定数据质量标准\n4.规划数据采集流程",
        "this_year": "今年积分分析：\n1.当前数据收集机制待建立\n2.建议优先搭建数据平台\n3.制定积分数据采集规范\n4.关注数据实时性和准确性"
    },
    
    # AI分析失败时的默认文本
    "ai_error": {
        "last_year": "积分变化分析报告：\n1.AI分析服务暂时不可用\n2.基于规则的分析显示数据正常\n3.积分体系运行稳定\n4.建议定期检查AI服务状态",
        "this_year": "积分趋势分析报告：\n1.智能分析功能维护中\n2.基础统计分析结果可用\n3.积分数据完整性良好\n4.后续将提供更深入的分析"
    }
}

def get_default_report(scenario: str, report_type: str) -> str:
    """
    获取默认分析报告
    
    Args:
        scenario: 场景类型 (last_year_only, this_year_only, no_data, ai_error)
        report_type: 报告类型 (last_year, this_year)
    
    Returns:
        str: 默认分析报告文本
    """
    return DEFAULT_REPORTS.get(scenario, DEFAULT_REPORTS["ai_error"]).get(report_type, "")