# -*- coding: utf-8 -*-
"""
积分变化AI分析模块
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class CreditChangeAiAnalyzer:
    """积分变化AI分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger(__name__)
        self.llm_service = None
        
        # 尝试导入LLM服务
        try:
            from services.llm_service import LLMService
            self.llm_service = LLMService()
            self.logger.info("LLM服务初始化成功")
        except ImportError:
            self.logger.warning("LLM服务不可用，将使用基于规则的分析")
    
    async def generate_all_credit_change_analysis(
        self, 
        this_year_data: List[Dict[str, Any]], 
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成完整的积分变化分析报告
        
        Args:
            this_year_data: 今年数据
            last_year_data: 去年数据
            
        Returns:
            Dict: 包含去年和今年分析报告的字典
        """
        try:
            self.logger.info("开始生成积分变化全部AI分析")
            
            # 如果有LLM服务，使用AI分析
            if self.llm_service:
                # 并发执行两个分析任务
                tasks = [
                    self.analyze_credit_change_last_year_data(last_year_data),
                    self.analyze_credit_change_this_year_data(this_year_data, last_year_data)
                ]
                
                results = await asyncio.gather(*tasks)
                
                analysis_dict = {
                    "credit_change_last_year_analysis_report": results[0],
                    "credit_change_this_year_analysis_report": results[1]
                }
            else:
                # 使用基于规则的分析
                analysis_dict = {
                    "credit_change_last_year_analysis_report": self._generate_rule_based_analysis(last_year_data, "last_year"),
                    "credit_change_this_year_analysis_report": self._generate_rule_based_analysis(this_year_data, "this_year", last_year_data)
                }
            
            self.logger.info("积分变化全部AI分析完成")
            return analysis_dict
            
        except Exception as e:
            self.logger.error(f"生成积分变化全部AI分析失败: {str(e)}")
            from .CreditChangePromt import get_default_report
            return {
                "credit_change_last_year_analysis_report": get_default_report("ai_error", "last_year"),
                "credit_change_this_year_analysis_report": get_default_report("ai_error", "this_year")
            }
    
    async def analyze_credit_change_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年积分变化数据
        
        Args:
            monthly_data: 去年月度数据列表，包含赠送积分和使用积分数量
        
        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                from .CreditChangePromt import get_default_report
                return get_default_report("no_data", "last_year")
            
            # 如果有LLM服务，使用AI分析
            if self.llm_service:
                from .CreditChangePromt import CREDIT_CHANGE_LAST_YEAR_AI_PROMPT
                
                # 构建完整的AI分析请求
                full_prompt = f"""
{CREDIT_CHANGE_LAST_YEAR_AI_PROMPT}

数据概况：
{self._format_credit_change_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在30字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：积分使用率、季节性特征、赠送策略效果、优化空间
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点30字以内，含标点符号）：
"""

                # 调用LLM服务进行分析
                analysis_result = await self.llm_service.generate_response(full_prompt)
                
                if analysis_result and analysis_result.strip():
                    self.logger.info("去年积分变化数据AI分析完成")
                    return analysis_result.strip()
                else:
                    self.logger.warning("AI分析返回空结果")
                    return self._generate_rule_based_analysis(monthly_data, "last_year")
            else:
                # 使用基于规则的分析
                return self._generate_rule_based_analysis(monthly_data, "last_year")

        except Exception as e:
            self.logger.error(f"去年积分变化数据AI分析失败: {str(e)}")
            return self._generate_rule_based_analysis(monthly_data, "last_year")
    
    async def analyze_credit_change_this_year_data(
        self, 
        this_year_data: List[Dict[str, Any]], 
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年积分变化数据（包含对比分析）
        
        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年同期数据列表（用于对比）
        
        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                from .CreditChangePromt import get_default_report
                return get_default_report("no_data", "this_year")
            
            # 如果有LLM服务，使用AI分析
            if self.llm_service:
                from .CreditChangePromt import CREDIT_CHANGE_THIS_YEAR_AI_PROMPT
                
                # 构建对比数据部分
                comparison_section = ""
                if last_year_data:
                    comparison_section = f"""
去年同期数据对比：
{self._format_credit_change_data_summary(last_year_data[:len(this_year_data)])}
"""

                # 构建完整的AI分析请求
                full_prompt = f"""
{CREDIT_CHANGE_THIS_YEAR_AI_PROMPT}

今年数据概况：
{self._format_credit_change_data_summary(this_year_data)}

{comparison_section}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在30字以内（标点符号不计入字数），突出动态变化和改进建议
3. 重点关注：同比变化、使用率提升、积分策略效果、未来优化方向
4. 必须标注具体数值和变化率
5. 每个洞察格式：动态变化 + 具体数据 + 优化建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点30字以内，含标点符号）：
"""

                # 调用LLM服务进行分析
                analysis_result = await self.llm_service.generate_response(full_prompt)
                
                if analysis_result and analysis_result.strip():
                    self.logger.info("今年积分变化数据AI分析完成")
                    return analysis_result.strip()
                else:
                    self.logger.warning("AI分析返回空结果")
                    return self._generate_rule_based_analysis(this_year_data, "this_year", last_year_data)
            else:
                # 使用基于规则的分析
                return self._generate_rule_based_analysis(this_year_data, "this_year", last_year_data)

        except Exception as e:
            self.logger.error(f"今年积分变化数据AI分析失败: {str(e)}")
            return self._generate_rule_based_analysis(this_year_data, "this_year", last_year_data)
    
    def _format_credit_change_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化积分变化数据摘要
        
        Args:
            monthly_data: 月度数据列表
        
        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"
        
        summary = []
        total_credit_reward = 0
        total_credit_consume = 0
        
        for item in monthly_data:
            month = item.get('month', '未知月份')
            credit_reward = item.get('credit_reward', 0)
            credit_consume = item.get('credit_consume', 0)
            
            # 计算使用率
            usage_rate = 0.0
            if credit_reward > 0:
                usage_rate = (credit_consume / credit_reward) * 100
            
            summary.append(f"{month}: 赠送{credit_reward}积分, 使用{credit_consume}积分, 使用率{usage_rate:.1f}%")
            
            total_credit_reward += credit_reward
            total_credit_consume += credit_consume
        
        # 添加汇总信息
        avg_usage_rate = 0.0
        if total_credit_reward > 0:
            avg_usage_rate = (total_credit_consume / total_credit_reward) * 100
        
        summary.append(f"\n汇总: 总赠送{total_credit_reward}积分, 总使用{total_credit_consume}积分")
        summary.append(f"平均使用率: {avg_usage_rate:.1f}%")
        
        return "\n".join(summary)
    
    def _generate_rule_based_analysis(
        self, 
        data: List[Dict[str, Any]], 
        period: str,
        comparison_data: List[Dict[str, Any]] = None
    ) -> str:
        """
        基于规则生成分析报告
        
        Args:
            data: 分析数据
            period: 时期（last_year/this_year）
            comparison_data: 对比数据
            
        Returns:
            str: 分析报告
        """
        if not data:
            from .CreditChangePromt import get_default_report
            return get_default_report("no_data", period)
        
        # 计算统计数据
        total_reward = sum(item.get('credit_reward', 0) for item in data)
        total_consume = sum(item.get('credit_consume', 0) for item in data)
        avg_reward = total_reward / len(data) if data else 0
        avg_consume = total_consume / len(data) if data else 0
        
        # 计算使用率
        usage_rate = (total_consume / total_reward * 100) if total_reward > 0 else 0
        
        # 找出峰值
        max_reward_month = max(data, key=lambda x: x.get('credit_reward', 0))
        max_consume_month = max(data, key=lambda x: x.get('credit_consume', 0))
        
        # 生成分析报告
        report_lines = []
        
        if period == "last_year":
            report_lines.append(f"去年积分变化分析：")
            report_lines.append(f"1.全年累计赠送{total_reward:,}积分，使用{total_consume:,}积分，使用率{usage_rate:.1f}%")
            report_lines.append(f"2.月均赠送{avg_reward:,.0f}积分，月均使用{avg_consume:,.0f}积分")
            report_lines.append(f"3.{max_reward_month['month']}赠送最多({max_reward_month['credit_reward']:,}积分)")
            report_lines.append(f"4.{max_consume_month['month']}使用最多({max_consume_month['credit_consume']:,}积分)")
        else:
            report_lines.append(f"今年积分变化分析：")
            report_lines.append(f"1.累计赠送{total_reward:,}积分，使用{total_consume:,}积分，使用率{usage_rate:.1f}%")
            report_lines.append(f"2.月均赠送{avg_reward:,.0f}积分，月均使用{avg_consume:,.0f}积分")
            
            if comparison_data:
                # 计算同比数据
                last_total_reward = sum(item.get('credit_reward', 0) for item in comparison_data[:len(data)])
                last_total_consume = sum(item.get('credit_consume', 0) for item in comparison_data[:len(data)])
                
                reward_change = ((total_reward - last_total_reward) / last_total_reward * 100) if last_total_reward else 0
                consume_change = ((total_consume - last_total_consume) / last_total_consume * 100) if last_total_consume else 0
                
                reward_trend = "增长" if reward_change > 0 else "下降"
                consume_trend = "增长" if consume_change > 0 else "下降"
                
                report_lines.append(f"3.积分赠送同比{reward_trend}{abs(reward_change):.1f}%")
                report_lines.append(f"4.积分使用同比{consume_trend}{abs(consume_change):.1f}%")
            else:
                report_lines.append(f"3.{max_reward_month['month']}赠送最多({max_reward_month['credit_reward']:,}积分)")
                report_lines.append(f"4.{max_consume_month['month']}使用最多({max_consume_month['credit_consume']:,}积分)")
        
        return "\n".join(report_lines)
    
    def _calculate_growth_rate(self, current: float, previous: float) -> float:
        """
        计算增长率
        
        Args:
            current: 当前值
            previous: 之前值
        
        Returns:
            float: 增长率（百分比）
        """
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return ((current - previous) / previous) * 100